#!/bin/bash

# Simple Node.js Service CloudFormation Stack Deletion Script

set -e

# Configuration
STACK_NAME="simple-nodejs-stack"
REGION="us-east-1"  # Change this to match your deployment region

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if AWS CLI is installed and configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    print_success "AWS CLI is properly configured"
}

# Function to check if stack exists
check_stack_exists() {
    if aws cloudformation describe-stacks --stack-name $STACK_NAME --region $REGION > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to get stack information before deletion
get_stack_info() {
    print_status "Getting stack information..."
    
    if check_stack_exists; then
        STACK_STATUS=$(aws cloudformation describe-stacks \
            --stack-name $STACK_NAME \
            --region $REGION \
            --query 'Stacks[0].StackStatus' \
            --output text)
        
        INSTANCE_ID=$(aws cloudformation describe-stacks \
            --stack-name $STACK_NAME \
            --region $REGION \
            --query 'Stacks[0].Outputs[?OutputKey==`InstanceId`].OutputValue' \
            --output text 2>/dev/null || echo "N/A")
        
        PUBLIC_IP=$(aws cloudformation describe-stacks \
            --stack-name $STACK_NAME \
            --region $REGION \
            --query 'Stacks[0].Outputs[?OutputKey==`InstancePublicIP`].OutputValue' \
            --output text 2>/dev/null || echo "N/A")
        
        echo
        print_status "Stack Information:"
        echo "  Stack Name: $STACK_NAME"
        echo "  Region: $REGION"
        echo "  Status: $STACK_STATUS"
        echo "  Instance ID: $INSTANCE_ID"
        echo "  Public IP: $PUBLIC_IP"
        
        return 0
    else
        print_warning "Stack '$STACK_NAME' not found in region $REGION"
        return 1
    fi
}

# Function to confirm deletion
confirm_deletion() {
    echo
    print_warning "This will permanently delete the following resources:"
    echo "  - EC2 Instance"
    echo "  - VPC and associated networking components"
    echo "  - Security Groups"
    echo "  - IAM Role and Instance Profile"
    echo "  - All data on the instance"
    echo
    print_warning "This action cannot be undone!"
    echo
    echo -n "Are you sure you want to delete the stack? (type 'yes' to confirm): "
    read confirmation
    
    if [[ "$confirmation" != "yes" ]]; then
        print_status "Deletion cancelled"
        exit 0
    fi
}

# Function to delete the stack
delete_stack() {
    print_status "Deleting CloudFormation stack: $STACK_NAME"
    
    aws cloudformation delete-stack \
        --stack-name $STACK_NAME \
        --region $REGION
    
    if [ $? -eq 0 ]; then
        print_success "Stack deletion initiated successfully!"
    else
        print_error "Failed to initiate stack deletion!"
        exit 1
    fi
}

# Function to wait for stack deletion to complete
wait_for_deletion() {
    print_status "Waiting for stack deletion to complete..."
    print_status "This may take several minutes..."
    
    local max_attempts=60
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if ! check_stack_exists; then
            print_success "Stack deletion completed successfully!"
            return 0
        fi
        
        STACK_STATUS=$(aws cloudformation describe-stacks \
            --stack-name $STACK_NAME \
            --region $REGION \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "DELETED")
        
        if [[ "$STACK_STATUS" == "DELETE_FAILED" ]]; then
            print_error "Stack deletion failed!"
            print_status "Check the CloudFormation console for details"
            return 1
        fi
        
        if [[ "$STACK_STATUS" == "DELETED" ]] || [[ "$STACK_STATUS" == "" ]]; then
            print_success "Stack deletion completed successfully!"
            return 0
        fi
        
        attempt=$((attempt + 1))
        echo -n "."
        sleep 10
    done
    
    print_warning "Stack deletion is taking longer than expected"
    print_status "Check the CloudFormation console for current status"
    return 1
}

# Function to list all stacks (helpful for finding the right stack name)
list_stacks() {
    print_status "Available CloudFormation stacks in region $REGION:"
    aws cloudformation list-stacks \
        --region $REGION \
        --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE \
        --query 'StackSummaries[].{Name:StackName,Status:StackStatus,Created:CreationTime}' \
        --output table
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -l, --list     List all available stacks"
    echo "  -s, --stack    Specify stack name (default: $STACK_NAME)"
    echo "  -r, --region   Specify AWS region (default: $REGION)"
    echo "  -f, --force    Skip confirmation prompt"
    echo ""
    echo "Examples:"
    echo "  $0                           # Delete default stack with confirmation"
    echo "  $0 --force                   # Delete default stack without confirmation"
    echo "  $0 --stack my-stack          # Delete specific stack"
    echo "  $0 --list                    # List all available stacks"
}

# Parse command line arguments
FORCE_DELETE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -l|--list)
            check_aws_cli
            list_stacks
            exit 0
            ;;
        -s|--stack)
            STACK_NAME="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -f|--force)
            FORCE_DELETE=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo "=========================================="
    echo "Simple Node.js Service Stack Deletion"
    echo "=========================================="
    echo
    
    check_aws_cli
    
    if ! get_stack_info; then
        exit 1
    fi
    
    if [[ "$FORCE_DELETE" != "true" ]]; then
        confirm_deletion
    fi
    
    delete_stack
    wait_for_deletion
    
    echo
    print_success "Stack deletion completed successfully!"
    echo
    print_status "All resources have been cleaned up."
}

# Run main function
main "$@"
