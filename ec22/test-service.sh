#!/bin/bash

# Test script for Simple Node.js Service
# This script tests all endpoints and verifies the service is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
HOST="localhost"
PORT="3000"
STACK_NAME="simple-nodejs-stack"
REGION="us-east-1"

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help         Show this help message"
    echo "  -H, --host HOST    Specify host (default: localhost)"
    echo "  -p, --port PORT    Specify port (default: 3000)"
    echo "  -s, --stack STACK  Get host from CloudFormation stack"
    echo "  -r, --region REG   AWS region for stack lookup (default: us-east-1)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Test localhost:3000"
    echo "  $0 --host *******                    # Test specific IP"
    echo "  $0 --stack simple-nodejs-stack       # Get IP from CloudFormation"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -H|--host)
            HOST="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -s|--stack)
            STACK_NAME="$2"
            # Get public IP from CloudFormation stack
            if command -v aws &> /dev/null; then
                print_status "Getting public IP from CloudFormation stack: $STACK_NAME"
                HOST=$(aws cloudformation describe-stacks \
                    --stack-name $STACK_NAME \
                    --region $REGION \
                    --query 'Stacks[0].Outputs[?OutputKey==`InstancePublicIP`].OutputValue' \
                    --output text 2>/dev/null || echo "")
                
                if [[ -z "$HOST" ]]; then
                    print_error "Could not get public IP from stack $STACK_NAME"
                    exit 1
                fi
                print_success "Found public IP: $HOST"
            else
                print_error "AWS CLI not found. Cannot lookup stack."
                exit 1
            fi
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

BASE_URL="http://$HOST:$PORT"

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    local data=$4
    local description=$5
    
    print_status "Testing: $method $endpoint - $description"
    
    if [[ "$method" == "GET" ]]; then
        response=$(curl -s -w "\n%{http_code}" "$BASE_URL$endpoint" || echo -e "\n000")
    elif [[ "$method" == "POST" ]]; then
        response=$(curl -s -w "\n%{http_code}" -X POST -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint" || echo -e "\n000")
    fi
    
    # Extract status code (last line) and body (everything else)
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [[ "$status_code" == "$expected_status" ]]; then
        print_success "✓ $method $endpoint - Status: $status_code"
        if command -v jq &> /dev/null && [[ "$body" =~ ^\{.*\}$ ]]; then
            echo "$body" | jq . 2>/dev/null || echo "$body"
        else
            echo "$body"
        fi
    else
        print_error "✗ $method $endpoint - Expected: $expected_status, Got: $status_code"
        echo "$body"
        return 1
    fi
    
    echo ""
    return 0
}

# Function to run all tests
run_tests() {
    local failed_tests=0
    
    echo "=========================================="
    echo "Testing Simple Node.js Service"
    echo "Base URL: $BASE_URL"
    echo "=========================================="
    echo ""
    
    # Test 1: Health check
    test_endpoint "GET" "/health" "200" "" "Health check endpoint" || ((failed_tests++))
    
    # Test 2: Root endpoint
    test_endpoint "GET" "/" "200" "" "Root welcome endpoint" || ((failed_tests++))
    
    # Test 3: API data endpoint
    test_endpoint "GET" "/api/data" "200" "" "Get sample data" || ((failed_tests++))
    
    # Test 4: POST data endpoint with valid data
    test_endpoint "POST" "/api/data" "201" '{"test": "data", "timestamp": "2024-01-01"}' "Post valid data" || ((failed_tests++))
    
    # Test 5: POST data endpoint with empty body (should fail)
    test_endpoint "POST" "/api/data" "400" '{}' "Post empty data (should fail)" || ((failed_tests++))
    
    # Test 6: Error simulation endpoint
    test_endpoint "GET" "/api/error" "400,401,403,404,500,503" "" "Error simulation (random status)" || {
        # For error endpoint, we accept any of the error status codes
        print_warning "Error endpoint returned unexpected status, but this is acceptable for random errors"
    }
    
    # Test 7: Non-existent endpoint (should return 404)
    test_endpoint "GET" "/nonexistent" "404" "" "Non-existent endpoint (should return 404)" || ((failed_tests++))
    
    echo "=========================================="
    if [[ $failed_tests -eq 0 ]]; then
        print_success "All tests passed! ✓"
    else
        print_error "$failed_tests test(s) failed! ✗"
    fi
    echo "=========================================="
    
    return $failed_tests
}

# Function to run load test
run_load_test() {
    local num_requests=${1:-10}
    
    print_status "Running load test with $num_requests concurrent requests..."
    
    local start_time=$(date +%s)
    local pids=()
    
    for i in $(seq 1 $num_requests); do
        curl -s "$BASE_URL/api/data" > /dev/null &
        pids+=($!)
    done
    
    # Wait for all requests to complete
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    print_success "Load test completed: $num_requests requests in ${duration}s"
}

# Function to check service health
check_service_health() {
    print_status "Checking service health..."
    
    # Test basic connectivity
    if curl -s -f "$BASE_URL/health" > /dev/null; then
        print_success "Service is responding"
        
        # Get health details
        health_response=$(curl -s "$BASE_URL/health")
        if command -v jq &> /dev/null; then
            echo "$health_response" | jq .
        else
            echo "$health_response"
        fi
        
        return 0
    else
        print_error "Service is not responding"
        return 1
    fi
}

# Main execution
main() {
    case "${1:-test}" in
        "test")
            run_tests
            ;;
        "health")
            check_service_health
            ;;
        "load")
            run_load_test "${2:-10}"
            ;;
        *)
            print_status "Available commands:"
            echo "  test   - Run all endpoint tests (default)"
            echo "  health - Check service health"
            echo "  load   - Run load test (optional: number of requests)"
            ;;
    esac
}

# Check if we're being called with a command
if [[ $# -gt 0 ]] && [[ "$1" =~ ^(test|health|load)$ ]]; then
    main "$@"
else
    # Default behavior - run tests
    run_tests
fi
