#!/bin/bash

# Verification script for Simple Node.js Service setup
# This script checks that all required files are present and properly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if file exists and is executable
check_file() {
    local file=$1
    local should_be_executable=$2
    
    if [[ -f "$file" ]]; then
        if [[ "$should_be_executable" == "true" ]]; then
            if [[ -x "$file" ]]; then
                print_success "✓ $file (executable)"
            else
                print_warning "⚠ $file (not executable)"
                chmod +x "$file"
                print_success "✓ Made $file executable"
            fi
        else
            print_success "✓ $file"
        fi
        return 0
    else
        print_error "✗ $file (missing)"
        return 1
    fi
}

# Function to validate CloudFormation template
validate_cloudformation() {
    print_status "Validating CloudFormation template..."
    
    if command -v aws &> /dev/null; then
        if aws cloudformation validate-template --template-body file://ec2.yaml > /dev/null 2>&1; then
            print_success "✓ CloudFormation template is valid"
        else
            print_error "✗ CloudFormation template validation failed"
            return 1
        fi
    else
        print_warning "⚠ AWS CLI not found, skipping CloudFormation validation"
    fi
}

# Function to check script syntax
check_script_syntax() {
    local script=$1
    
    if bash -n "$script" 2>/dev/null; then
        print_success "✓ $script syntax is valid"
    else
        print_error "✗ $script has syntax errors"
        return 1
    fi
}

# Main verification function
main() {
    echo "=========================================="
    echo "Simple Node.js Service Setup Verification"
    echo "=========================================="
    echo ""
    
    local errors=0
    
    # Check required files
    print_status "Checking required files..."
    
    check_file "ec2.yaml" "false" || ((errors++))
    check_file "deploy.sh" "true" || ((errors++))
    check_file "delete.sh" "true" || ((errors++))
    check_file "main-setup.sh" "true" || ((errors++))
    check_file "setup-nodejs-service.sh" "true" || ((errors++))
    check_file "setup-otel-collector.sh" "true" || ((errors++))
    check_file "test-service.sh" "true" || ((errors++))
    check_file "README.md" "false" || ((errors++))
    check_file "DEPLOYMENT_SUMMARY.md" "false" || ((errors++))
    
    echo ""
    
    # Validate CloudFormation template
    validate_cloudformation || ((errors++))
    
    echo ""
    
    # Check script syntax
    print_status "Checking script syntax..."
    
    for script in *.sh; do
        if [[ -f "$script" ]]; then
            check_script_syntax "$script" || ((errors++))
        fi
    done
    
    echo ""
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    
    if command -v aws &> /dev/null; then
        print_success "✓ AWS CLI is installed"
        
        if aws sts get-caller-identity &> /dev/null; then
            print_success "✓ AWS CLI is configured"
        else
            print_warning "⚠ AWS CLI is not configured (run 'aws configure')"
        fi
    else
        print_error "✗ AWS CLI is not installed"
        ((errors++))
    fi
    
    if command -v curl &> /dev/null; then
        print_success "✓ curl is available"
    else
        print_warning "⚠ curl is not available (needed for testing)"
    fi
    
    if command -v jq &> /dev/null; then
        print_success "✓ jq is available (for pretty JSON output)"
    else
        print_warning "⚠ jq is not available (optional, for pretty JSON output)"
    fi
    
    echo ""
    
    # Summary
    print_status "Verification Summary:"
    
    if [[ $errors -eq 0 ]]; then
        print_success "✓ All checks passed! Ready for deployment."
        echo ""
        print_status "Next steps:"
        echo "1. Run './deploy.sh' to deploy the stack"
        echo "2. Wait for deployment to complete (5-10 minutes)"
        echo "3. Run './test-service.sh --stack simple-nodejs-stack' to test"
        echo "4. Access your service at http://YOUR_PUBLIC_IP:3000"
        echo ""
        print_status "For detailed instructions, see README.md"
    else
        print_error "✗ $errors error(s) found. Please fix them before deployment."
        return 1
    fi
    
    echo "=========================================="
}

# Run verification
main "$@"
