#!/bin/bash
set -e

echo "=========================================="
echo "Simple Node.js Service Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a service is running
check_service_status() {
    local service_name=$1
    if systemctl is-active --quiet $service_name; then
        print_success "$service_name is running"
        return 0
    else
        print_error "$service_name is not running"
        return 1
    fi
}

# Function to wait for a service to be ready
wait_for_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=0
    
    print_status "Waiting for $service_name to be ready on port $port..."
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s -f "http://localhost:$port/health" > /dev/null 2>&1 || \
           curl -s -f "http://localhost:$port" > /dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        attempt=$((attempt + 1))
        echo -n "."
        sleep 2
    done
    
    print_warning "$service_name may still be starting up"
    return 1
}

# Create setup scripts inline if they don't exist
create_setup_scripts() {
    print_status "Creating setup scripts..."
    
    # Create OTEL collector setup script if it doesn't exist
    if [ ! -f "/opt/simple-nodejs/setup-otel-collector.sh" ]; then
        print_status "Creating OTEL collector setup script..."
        # The script content would be embedded here in a real deployment
        # For now, we'll assume it exists or download it
        curl -o /opt/simple-nodejs/setup-otel-collector.sh \
            https://raw.githubusercontent.com/your-repo/setup-otel-collector.sh || \
            cp /opt/simple-nodejs/setup-otel-collector.sh /opt/simple-nodejs/setup-otel-collector.sh 2>/dev/null || \
            print_warning "OTEL collector setup script not found"
    fi
    
    # Create Node.js setup script if it doesn't exist
    if [ ! -f "/opt/simple-nodejs/setup-nodejs-service.sh" ]; then
        print_status "Creating Node.js service setup script..."
        curl -o /opt/simple-nodejs/setup-nodejs-service.sh \
            https://raw.githubusercontent.com/your-repo/setup-nodejs-service.sh || \
            cp /opt/simple-nodejs/setup-nodejs-service.sh /opt/simple-nodejs/setup-nodejs-service.sh 2>/dev/null || \
            print_warning "Node.js service setup script not found"
    fi
    
    # Make scripts executable
    chmod +x /opt/simple-nodejs/*.sh 2>/dev/null || true
}

# Main setup function
main_setup() {
    print_status "Starting main setup process..."
    
    # Change to application directory
    cd /opt/simple-nodejs
    
    # Create setup scripts if needed
    create_setup_scripts
    
    # Step 1: Setup OpenTelemetry Collector
    print_status "Step 1: Setting up OpenTelemetry Collector..."
    if [ -f "setup-otel-collector.sh" ]; then
        chmod +x setup-otel-collector.sh
        ./setup-otel-collector.sh
        print_success "OTEL Collector setup completed"
    else
        print_error "OTEL Collector setup script not found"
        exit 1
    fi
    
    # Step 2: Setup Node.js Service
    print_status "Step 2: Setting up Node.js service..."
    if [ -f "setup-nodejs-service.sh" ]; then
        chmod +x setup-nodejs-service.sh
        ./setup-nodejs-service.sh
        print_success "Node.js service setup completed"
    else
        print_error "Node.js service setup script not found"
        exit 1
    fi
    
    # Step 3: Start services
    print_status "Step 3: Starting services..."
    
    # Reload systemd daemon
    systemctl daemon-reload
    
    # Start and enable OTEL Collector
    print_status "Starting OTEL Collector..."
    systemctl enable otel-collector
    systemctl start otel-collector
    sleep 5
    
    # Start and enable Node.js service
    print_status "Starting Node.js service..."
    systemctl enable simple-nodejs
    systemctl start simple-nodejs
    sleep 5
    
    # Step 4: Verify services
    print_status "Step 4: Verifying services..."
    
    check_service_status "otel-collector"
    check_service_status "simple-nodejs"
    
    # Wait for services to be ready
    wait_for_service "Node.js" "3000"
    
    # Step 5: Create health check script
    print_status "Step 5: Creating health check script..."
    create_health_check_script
    
    # Step 6: Setup log rotation
    print_status "Step 6: Setting up log rotation..."
    setup_log_rotation
    
    print_success "All setup completed successfully!"
    
    # Display service information
    display_service_info
}

# Function to create health check script
create_health_check_script() {
    cat > /opt/simple-nodejs/health-check.sh << 'EOF'
#!/bin/bash

echo "=========================================="
echo "Simple Node.js Service Health Check"
echo "=========================================="

# Check OTEL Collector
echo "Checking OTEL Collector..."
if systemctl is-active --quiet otel-collector; then
    echo "✓ OTEL Collector is running"
else
    echo "✗ OTEL Collector is not running"
fi

# Check Node.js service
echo "Checking Node.js service..."
if systemctl is-active --quiet simple-nodejs; then
    echo "✓ Node.js service is running"
else
    echo "✗ Node.js service is not running"
fi

# Check service endpoints
echo "Checking service endpoints..."
if curl -s -f "http://localhost:3000/health" > /dev/null; then
    echo "✓ Node.js service endpoint is responding"
else
    echo "✗ Node.js service endpoint is not responding"
fi

# Display service logs (last 10 lines)
echo ""
echo "Recent Node.js service logs:"
journalctl -u simple-nodejs -n 10 --no-pager

echo ""
echo "Recent OTEL Collector logs:"
journalctl -u otel-collector -n 10 --no-pager
EOF

    chmod +x /opt/simple-nodejs/health-check.sh
}

# Function to setup log rotation
setup_log_rotation() {
    cat > /etc/logrotate.d/simple-nodejs << 'EOF'
/opt/simple-nodejs/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 ec2-user ec2-user
    postrotate
        systemctl reload simple-nodejs > /dev/null 2>&1 || true
    endscript
}
EOF
}

# Function to display service information
display_service_info() {
    echo ""
    print_success "=========================================="
    print_success "Setup Complete! Service Information:"
    print_success "=========================================="
    echo ""
    
    # Get public IP
    PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "localhost")
    
    echo "Service URLs:"
    echo "  Node.js Service: http://$PUBLIC_IP:3000"
    echo "  Health Check:    http://$PUBLIC_IP:3000/health"
    echo "  API Endpoint:    http://$PUBLIC_IP:3000/api/data"
    echo ""
    echo "Available endpoints:"
    echo "  GET  /           - Welcome message"
    echo "  GET  /health     - Health check"
    echo "  GET  /api/data   - Get sample data"
    echo "  POST /api/data   - Submit data"
    echo "  GET  /api/error  - Simulate errors"
    echo ""
    echo "Management commands:"
    echo "  Health check:    /opt/simple-nodejs/health-check.sh"
    echo "  Service logs:    journalctl -u simple-nodejs -f"
    echo "  OTEL logs:       journalctl -u otel-collector -f"
    echo ""
    echo "Service status:"
    systemctl status simple-nodejs --no-pager -l
}

# Run main setup
main_setup
