#!/bin/bash
set -e

echo "Starting distributed services setup..."

# Create log directory
mkdir -p /opt/distributed-services/logs

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a /opt/distributed-services/logs/setup.log
}

log "Setting up OpenTelemetry Collector..."
bash /opt/distributed-services/setup-otel-collector.sh

log "Setting up Node.js service..."
bash /opt/distributed-services/setup-nodejs-service.sh

log "Setting up Python service..."
bash /opt/distributed-services/setup-python-service.sh

log "Setting up Java service..."
bash /opt/distributed-services/setup-java-service.sh

log "Setting up Go service..."
bash /opt/distributed-services/setup-go-service.sh

log "Setting up CloudWatch Agent..."
bash /opt/distributed-services/setup-cloudwatch-agent.sh

# Set proper permissions
chown -R ec2-user:ec2-user /opt/distributed-services
chmod +x /opt/distributed-services/go-service/go-service

# Create log directories for services
mkdir -p /opt/distributed-services/nodejs-service/logs
mkdir -p /opt/distributed-services/python-service/logs
mkdir -p /opt/distributed-services/java-service/logs
mkdir -p /opt/distributed-services/go-service/logs
chown -R ec2-user:ec2-user /opt/distributed-services/*/logs

# Create health check script
cat > /opt/distributed-services/health-check.sh << 'EOF'
#!/bin/bash
echo "=== Distributed Services Health Check ==="
echo "Timestamp: $(date)"
echo

services=("nodejs-service:3000" "python-service:5000" "java-service:8080" "go-service:8090")

for service in "${services[@]}"; do
    name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)

    echo "Checking $name on port $port..."
    if curl -s -f http://localhost:$port/health > /dev/null; then
        echo "[OK] $name is healthy"
    else
        echo "[FAIL] $name is not responding"
    fi
done

echo
echo "=== Service Status ==="
systemctl status otel-collector --no-pager -l
systemctl status nodejs-service --no-pager -l
systemctl status python-service --no-pager -l
systemctl status java-service --no-pager -l
systemctl status go-service --no-pager -l
EOF

chmod +x /opt/distributed-services/health-check.sh

# Create load testing script to generate diverse traffic
cat > /opt/distributed-services/generate-traffic.sh << 'EOF'
#!/bin/bash
echo "=== Generating Diverse Traffic for Telemetry ==="
echo "This script generates various HTTP status codes for realistic telemetry data"
echo "Press Ctrl+C to stop"
echo

# Function to make requests with random delays
make_request() {
    local url=$1
    local method=${2:-GET}
    local data=$3

    if [ "$method" = "POST" ]; then
        if [ -n "$data" ]; then
            curl -s -X POST -H "Content-Type: application/json" -d "$data" "$url" > /dev/null 2>&1
        else
            curl -s -X POST "$url" > /dev/null 2>&1
        fi
    else
        curl -s "$url" > /dev/null 2>&1
    fi

    # Random delay between requests
    sleep $(echo "scale=2; $RANDOM/32767 * 2" | bc -l) 2>/dev/null || sleep 1
}

# Arrays of endpoints that generate different status codes
declare -a success_endpoints=(
    "http://localhost:3000/health"
    "http://localhost:5000/health"
    "http://localhost:8080/health"
    "http://localhost:8090/health"
    "http://localhost:3000/api/data"
    "http://localhost:5000/api/data"
    "http://localhost:8080/api/data"
    "http://localhost:8090/api/data"
    "http://localhost:3000/api/orders/12345"
    "http://localhost:5000/api/products/widget123"
    "http://localhost:8080/api/users/user456"
    "http://localhost:8090/api/inventory/item789"
)

declare -a error_endpoints=(
    "http://localhost:3000/api/orders/invalid"
    "http://localhost:3000/api/orders/notfound"
    "http://localhost:3000/api/orders/unauthorized"
    "http://localhost:5000/api/products/invalid"
    "http://localhost:5000/api/products/notfound"
    "http://localhost:5000/api/products/discontinued"
    "http://localhost:8080/api/users/invalid"
    "http://localhost:8080/api/users/notfound"
    "http://localhost:8080/api/users/forbidden"
    "http://localhost:8090/api/inventory/invalid"
    "http://localhost:8090/api/inventory/notfound"
    "http://localhost:8090/api/inventory/restricted"
)

declare -a slow_endpoints=(
    "http://localhost:3000/api/timeout"
    "http://localhost:5000/api/heavy"
    "http://localhost:8080/api/slow"
    "http://localhost:8090/api/stress"
)

declare -a post_endpoints=(
    "http://localhost:3000/api/process"
    "http://localhost:5000/api/calculate"
    "http://localhost:8090/api/validate"
)

# JSON payloads for POST requests
declare -a json_payloads=(
    '{"operation":"add","a":10,"b":5}'
    '{"operation":"divide","a":10,"b":0}'
    '{"operation":"invalid","a":10,"b":5}'
    '{"type":"valid","data":"test"}'
    '{"type":"invalid","data":"test"}'
    '{"incomplete":"data"}'
)

# Main traffic generation loop
counter=0
while true; do
    counter=$((counter + 1))

    # Generate different types of traffic with different probabilities
    rand=$((RANDOM % 100))

    if [ $rand -lt 50 ]; then
        # 50% success requests
        endpoint=${success_endpoints[$RANDOM % ${#success_endpoints[@]}]}
        make_request "$endpoint"
        echo "[$counter] Success request: $endpoint"

    elif [ $rand -lt 75 ]; then
        # 25% error requests
        endpoint=${error_endpoints[$RANDOM % ${#error_endpoints[@]}]}
        make_request "$endpoint"
        echo "[$counter] Error request: $endpoint"

    elif [ $rand -lt 85 ]; then
        # 10% slow requests
        endpoint=${slow_endpoints[$RANDOM % ${#slow_endpoints[@]}]}
        make_request "$endpoint"
        echo "[$counter] Slow request: $endpoint"

    elif [ $rand -lt 95 ]; then
        # 10% POST requests
        endpoint=${post_endpoints[$RANDOM % ${#post_endpoints[@]}]}
        payload=${json_payloads[$RANDOM % ${#json_payloads[@]}]}
        make_request "$endpoint" "POST" "$payload"
        echo "[$counter] POST request: $endpoint"

    else
        # 5% requests with explicit error simulation
        base_endpoints=("http://localhost:3000/api/data" "http://localhost:5000/api/data" "http://localhost:8080/api/data" "http://localhost:8090/api/data")
        endpoint=${base_endpoints[$RANDOM % ${#base_endpoints[@]}]}
        make_request "${endpoint}?simulate_error=true"
        echo "[$counter] Simulated error: $endpoint"
    fi

    # Occasional burst of requests
    if [ $((counter % 50)) -eq 0 ]; then
        echo "[$counter] Generating traffic burst..."
        for i in {1..10}; do
            endpoint=${success_endpoints[$RANDOM % ${#success_endpoints[@]}]}
            make_request "$endpoint"
        done
    fi
done
EOF

chmod +x /opt/distributed-services/generate-traffic.sh

# Copy verification script
cp /opt/distributed-services/verify-log-trace-correlation.sh /opt/distributed-services/
chmod +x /opt/distributed-services/verify-log-trace-correlation.sh

# Enable and start services
log "Enabling and starting services..."
systemctl daemon-reload

# Enable services
systemctl enable otel-collector
systemctl enable nodejs-service
systemctl enable python-service
systemctl enable java-service
systemctl enable go-service

# Start OTEL Collector first
log "Starting OpenTelemetry Collector..."
systemctl start otel-collector
sleep 10

# Start application services
log "Starting application services..."
systemctl start nodejs-service
systemctl start python-service
systemctl start java-service
systemctl start go-service

# Start CloudWatch Agent
log "Starting CloudWatch Agent..."
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json -s

# Enable and start SSM Agent
log "Starting SSM Agent..."
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent

# Wait for services to start
log "Waiting for services to start..."
sleep 30

# Run health check
log "Running initial health check..."
/opt/distributed-services/health-check.sh > /opt/distributed-services/logs/initial-health-check.log 2>&1

# Wait a bit more for services to fully initialize
log "Waiting for services to fully initialize..."
sleep 30

# Run log-trace correlation verification
log "Running log-trace correlation verification..."
/opt/distributed-services/verify-log-trace-correlation.sh > /opt/distributed-services/logs/log-trace-verification.log 2>&1

log "Distributed services setup completed successfully!"
echo "Setup completed at $(date)" > /var/log/setup-complete.log
