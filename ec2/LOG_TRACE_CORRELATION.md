# 🔗 Log-Trace Correlation Implementation

## 🎯 Overview

The distributed services now implement **perfect 1:1 correlation** between logs and traces, ensuring that every HTTP request generates exactly one structured log entry that matches the corresponding OpenTelemetry trace.

## 📊 Correlation Guarantees

### ✅ **1:1 Correlation**
- **One HTTP Request** = **One Log Entry** = **One Trace Span**
- No missing logs for any status code
- No duplicate logs for single requests
- Perfect correlation across all services

### ✅ **Status Code Consistency**
- Log `status_code` field matches HTTP response status
- Trace span status matches HTTP response status
- Error logs (4xx/5xx) correlate with error traces
- Success logs (2xx) correlate with successful traces

### ✅ **Request ID Matching**
- Unique `request_id` shared between logs and traces
- Format: `{service}-{counter}` (e.g., `nodejs-123`, `python-456`)
- Enables precise correlation in observability tools
- Facilitates distributed tracing across service calls

### ✅ **Timing Correlation**
- Log `duration_ms` matches trace span duration
- Consistent timing measurements across telemetry types
- Enables performance correlation analysis

## 🏗️ Implementation Details

### Structured Log Format
```json
{
  "timestamp": "2024-01-15T10:30:45.123Z",
  "service": "nodejs",
  "method": "GET",
  "path": "/api/data",
  "status_code": 200,
  "request_id": "nodejs-123",
  "duration_ms": 45.67,
  "error": null,
  "level": "INFO"
}
```

### Error Log Example
```json
{
  "timestamp": "2024-01-15T10:31:12.456Z",
  "service": "python",
  "method": "GET", 
  "path": "/api/products/notfound",
  "status_code": 404,
  "request_id": "python-789",
  "duration_ms": 12.34,
  "error": "Product not found",
  "level": "WARN"
}
```

### Log Level Mapping
- **INFO**: Status codes 200-299 (Success)
- **WARN**: Status codes 400-499 (Client errors)
- **ERROR**: Status codes 500-599 (Server errors)

## 🔧 Technical Implementation

### Service-Level Logging
Each service implements structured logging with:

#### Node.js
```javascript
function logRequest(method, path, statusCode, requestId, duration, error) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    service: 'nodejs',
    method, path, status_code: statusCode,
    request_id: requestId, duration_ms: duration,
    error, level: statusCode >= 500 ? 'ERROR' : statusCode >= 400 ? 'WARN' : 'INFO'
  };
  console.log(JSON.stringify(logEntry));
}
```

#### Python
```python
def log_request(method, path, status_code, request_id, duration_ms=None, error=None):
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'service': 'python', 'method': method, 'path': path,
        'status_code': status_code, 'request_id': request_id,
        'duration_ms': duration_ms, 'error': error,
        'level': 'ERROR' if status_code >= 500 else 'WARN' if status_code >= 400 else 'INFO'
    }
    print(json.dumps(log_entry))
```

#### Java
```java
private void logRequest(String method, String path, int statusCode, 
                       String requestId, Long durationMs, String error) {
    Map<String, Object> logEntry = new HashMap<>();
    logEntry.put("timestamp", Instant.now().toString());
    logEntry.put("service", "java");
    // ... populate other fields
    System.out.println(objectMapper.writeValueAsString(logEntry));
}
```

#### Go
```go
func logRequest(method, path string, statusCode int, requestID string, 
               duration time.Duration, err error) {
    logEntry := LogEntry{
        Timestamp: time.Now().Format(time.RFC3339),
        Service: "go", Method: method, Path: path,
        StatusCode: statusCode, RequestID: requestID,
        DurationMs: float64(duration.Nanoseconds()) / 1e6,
        // ... other fields
    }
    fmt.Println(string(logJSON))
}
```

### SystemD Integration
Services configured with:
```ini
StandardOutput=journal
StandardError=journal
SyslogIdentifier={service-name}
```

### OTEL Collector Configuration
```yaml
receivers:
  journald:
    directory: /var/log/journal
    units: [nodejs-service, python-service, java-service, go-service]
    operators:
      - type: json_parser
        if: 'body matches "^\\{"'
        parse_from: body
        parse_to: attributes
```

## 🧪 Verification Methods

### Automated Verification Script
```bash
sudo /opt/distributed-services/verify-log-trace-correlation.sh
```

**Verification Steps:**
1. **Generate test requests** with known status codes
2. **Extract request IDs** from HTTP responses
3. **Search logs** for matching request IDs and status codes
4. **Validate JSON structure** of log entries
5. **Count logs by status code** for statistics
6. **Verify OTEL collector** is processing logs

### Manual Verification
```bash
# View structured logs in real-time
journalctl -u nodejs-service -f -o json | jq -r '.MESSAGE | select(test("^\\{.*\\}$"))'

# Count logs by status code
journalctl -u python-service --since "1 hour ago" -o json | \
  jq -r 'select(.MESSAGE | test("^\\{.*\\}$")) | .MESSAGE' | \
  jq -r '.status_code' | sort | uniq -c

# Find logs for specific request ID
journalctl -u java-service --since "10 minutes ago" | grep "java-123"
```

## 📈 Observability Benefits

### In Coralogix Dashboard
- **Correlated Views**: Logs and traces appear together for each request
- **Status Code Metrics**: Accurate error rates from both logs and traces
- **Performance Analysis**: Duration correlation between logs and spans
- **Error Investigation**: Detailed error context in both telemetry types

### In CloudWatch
- **Log Groups**: Structured logs with searchable fields
- **Metrics**: Custom metrics derived from log status codes
- **Alarms**: Alerts based on error rates from logs
- **Dashboards**: Combined log and trace visualizations

### Troubleshooting Advantages
- **Request Tracing**: Follow a request across all services using request_id
- **Error Context**: Rich error information in both logs and traces
- **Performance Debugging**: Precise timing correlation
- **Service Health**: Accurate health metrics from consistent telemetry

## 🎯 Quality Assurance

### Guaranteed Correlation
✅ **Every 200 OK response** has a corresponding INFO log  
✅ **Every 4xx error response** has a corresponding WARN log  
✅ **Every 5xx error response** has a corresponding ERROR log  
✅ **Every trace span** has a corresponding log entry  
✅ **Every request_id** appears in both logs and traces  
✅ **Every status_code** matches between HTTP, logs, and traces  

### Testing Coverage
- **Health endpoints**: 200 OK responses
- **Success scenarios**: 200/201 responses with data
- **Client errors**: 400, 401, 403, 404, 408, 410, 422, 429
- **Server errors**: 500, 502, 503, 504
- **Timeout scenarios**: Request timeouts and processing delays
- **POST operations**: Data validation and processing errors

This implementation ensures **perfect observability correlation** where every HTTP interaction is captured consistently across all telemetry signals! 🎯📊
