#!/bin/bash
set -e

echo "Setting up Node.js service..."

# Create Node.js service directory
mkdir -p /opt/distributed-services/nodejs-service
cd /opt/distributed-services/nodejs-service

# Create package.json
cat > package.json << 'EOF'
{
  "name": "nodejs-service",
  "version": "1.0.0",
  "description": "Node.js service with OpenTelemetry",
  "main": "app.js",
  "scripts": {
    "start": "node app.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "@opentelemetry/api": "^1.7.0",
    "@opentelemetry/sdk-node": "^0.45.0",
    "@opentelemetry/auto-instrumentations-node": "^0.40.0",
    "@opentelemetry/exporter-otlp-grpc": "^0.45.0",
    "axios": "^1.6.0"
  }
}
EOF

# Create Node.js application
cat > app.js << 'EOF'
const { NodeSDK } = require('@opentelemetry/sdk-node');
const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
const { OTLPTraceExporter } = require('@opentelemetry/exporter-otlp-grpc');

const sdk = new NodeSDK({
  traceExporter: new OTLPTraceExporter({
    url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT || 'http://localhost:4317',
  }),
  instrumentations: [getNodeAutoInstrumentations()],
});

sdk.start();

const express = require('express');
const axios = require('axios');
const app = express();
const port = 3000;

let requestCounter = 0;

app.use(express.json());

// Structured logging function
function logRequest(method, path, statusCode, requestId, duration = null, error = null) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    service: 'nodejs',
    method: method,
    path: path,
    status_code: statusCode,
    request_id: requestId,
    duration_ms: duration,
    error: error,
    level: statusCode >= 500 ? 'ERROR' : statusCode >= 400 ? 'WARN' : 'INFO'
  };
  console.log(JSON.stringify(logEntry));
}

// Middleware to simulate random delays
app.use((req, res, next) => {
  if (Math.random() < 0.1) { // 10% chance of delay
    setTimeout(next, Math.random() * 1000 + 500); // 500-1500ms delay
  } else {
    next();
  }
});

app.get('/health', (req, res) => {
  const startTime = Date.now();
  const response = {
    service: 'nodejs',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    requests_processed: requestCounter
  };
  const duration = Date.now() - startTime;
  logRequest('GET', '/health', 200, 'health-check', duration);
  res.json(response);
});

app.get('/api/data', async (req, res) => {
  const startTime = Date.now();
  requestCounter++;
  const requestId = `nodejs-${requestCounter}`;

  // Simulate various response scenarios
  const simulateError = req.query.simulate_error === 'true' || Math.random() < 0.12; // 12% chance of error

  if (simulateError) {
    return simulateErrorResponse(res, requestId, startTime);
  }

  try {
    // Call Python service
    const pythonResponse = await axios.get('http://localhost:5000/health', { timeout: 5000 });
    const duration = Date.now() - startTime;
    const response = {
      service: 'nodejs',
      data: 'Hello from Node.js service',
      python_service: pythonResponse.data,
      timestamp: new Date().toISOString(),
      request_id: requestId
    };
    logRequest('GET', '/api/data', 200, requestId, duration);
    res.json(response);
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorResponse = {
      service: 'nodejs',
      error: 'Failed to call Python service: ' + error.message,
      timestamp: new Date().toISOString(),
      request_id: requestId
    };
    logRequest('GET', '/api/data', 503, requestId, duration, error.message);
    res.status(503).json(errorResponse);
  }
});

app.get('/api/orders/:orderId', (req, res) => {
  const startTime = Date.now();
  requestCounter++;
  const requestId = `nodejs-${requestCounter}`;
  const { orderId } = req.params;

  // Simulate order validation
  if (!orderId || orderId.trim() === '') {
    const duration = Date.now() - startTime;
    const errorMsg = 'Order ID is required';
    logRequest('GET', `/api/orders/${orderId}`, 400, requestId, duration, errorMsg);
    return res.status(400).json({
      service: 'nodejs',
      error: errorMsg,
      timestamp: new Date().toISOString(),
      request_id: requestId
    });
  }

  if (orderId === 'invalid') {
    return res.status(400).json({
      service: 'nodejs',
      error: 'Invalid order ID format',
      timestamp: new Date().toISOString(),
      request_id: requestId
    });
  }

  if (orderId === 'notfound') {
    return res.status(404).json({
      service: 'nodejs',
      error: 'Order not found',
      timestamp: new Date().toISOString(),
      request_id: requestId
    });
  }

  if (orderId === 'unauthorized') {
    return res.status(401).json({
      service: 'nodejs',
      error: 'Unauthorized access to order',
      timestamp: new Date().toISOString(),
      request_id: requestId
    });
  }

  // Simulate successful order data
  res.json({
    service: 'nodejs',
    order_id: orderId,
    status: 'completed',
    amount: Math.floor(Math.random() * 1000) + 10,
    currency: 'USD',
    timestamp: new Date().toISOString(),
    request_id: requestId
  });
});

app.get('/api/timeout', async (req, res) => {
  requestCounter++;
  const requestId = `nodejs-${requestCounter}`;

  try {
    // Simulate long-running operation that might timeout
    await new Promise(resolve => setTimeout(resolve, Math.random() * 8000 + 2000)); // 2-10 seconds

    res.json({
      service: 'nodejs',
      data: 'Long operation completed',
      processing_time_ms: Math.floor(Math.random() * 8000 + 2000),
      timestamp: new Date().toISOString(),
      request_id: requestId
    });
  } catch (error) {
    res.status(500).json({
      service: 'nodejs',
      error: 'Operation timed out',
      timestamp: new Date().toISOString(),
      request_id: requestId
    });
  }
});

app.post('/api/process', (req, res) => {
  requestCounter++;
  const requestId = `nodejs-${requestCounter}`;

  // Simulate processing with various outcomes
  const outcome = Math.random();

  if (outcome < 0.1) { // 10% chance of validation error
    return res.status(422).json({
      service: 'nodejs',
      error: 'Validation failed: Invalid input data',
      timestamp: new Date().toISOString(),
      request_id: requestId
    });
  }

  if (outcome < 0.15) { // 5% chance of server error
    return res.status(500).json({
      service: 'nodejs',
      error: 'Internal processing error',
      timestamp: new Date().toISOString(),
      request_id: requestId
    });
  }

  // Successful processing
  res.status(201).json({
    service: 'nodejs',
    message: 'Data processed successfully',
    processed_at: new Date().toISOString(),
    request_id: requestId
  });
});

function simulateErrorResponse(res, requestId, startTime) {
  const errorTypes = [
    { status: 400, error: 'Bad request: Missing required parameters' },
    { status: 401, error: 'Unauthorized: Invalid authentication token' },
    { status: 403, error: 'Forbidden: Insufficient permissions' },
    { status: 429, error: 'Too many requests: Rate limit exceeded' },
    { status: 500, error: 'Internal server error: Database connection failed' },
    { status: 502, error: 'Bad gateway: Upstream service error' },
    { status: 503, error: 'Service unavailable: System maintenance' }
  ];

  const randomError = errorTypes[Math.floor(Math.random() * errorTypes.length)];
  const duration = Date.now() - startTime;

  logRequest('GET', '/api/data', randomError.status, requestId, duration, randomError.error);

  res.status(randomError.status).json({
    service: 'nodejs',
    error: randomError.error,
    timestamp: new Date().toISOString(),
    request_id: requestId
  });
}

app.listen(port, () => {
  console.log('Node.js service listening at http://localhost:' + port);
});
EOF

# Install dependencies
npm install

# Create systemd service
cat > /etc/systemd/system/nodejs-service.service << 'EOF'
[Unit]
Description=Node.js Service
After=network.target otel-collector.service
Requires=otel-collector.service

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/distributed-services/nodejs-service
ExecStart=/usr/bin/node app.js
Restart=always
RestartSec=5
Environment=NODE_ENV=production
EnvironmentFile=/opt/distributed-services/.env
StandardOutput=journal
StandardError=journal
SyslogIdentifier=nodejs-service

[Install]
WantedBy=multi-user.target
EOF

echo "Node.js service setup completed"
